import os
import time
import tempfile
import uuid
import re
import logging
import asyncio
from typing import Optional

import torch
import torchaudio
from fastapi import Fast<PERSON><PERSON>, File, UploadFile, Form, HTTPException
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from faster_whisper import <PERSON>hisperModel
from transformers import CsmForConditionalGeneration, AutoProcessor

from generator import load_csm_1b, Segment

# 🔒 Set visible CUDA device to GPU 1 (makes it appear as cuda:0)
os.environ["CUDA_VISIBLE_DEVICES"] = "1"

# 🧹 Clear GPU memory before loading model
torch.cuda.empty_cache()
torch.cuda.reset_peak_memory_stats()

# ✅ Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s | %(levelname)s | %(name)s | %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger("CSMOptimizedAPI")

# 🎤 FastAPI app
app = FastAPI(title="CSM Optimized Voice Generation API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Optional: Request logging middleware with concurrency info
@app.middleware("http")
async def log_requests(request, call_next):
    global active_requests
    logger.info(f"📡 Incoming request: {request.method} {request.url} (Active: {active_requests})")
    start_time = time.time()
    response = await call_next(request)
    duration = time.time() - start_time
    logger.info(f"↩️ Completed response: {response.status_code} for {request.url} in {duration:.2f}s")
    return response

# Global variables for models
generator = None
whisper_model = None
device = None
model = None
processor = None

# 🔒 Concurrency control: Locks to prevent simultaneous model access
csm_model_lock = asyncio.Lock()
whisper_model_lock = asyncio.Lock()

# 📊 Request tracking for monitoring
active_requests = 0
total_requests = 0
request_queue_size = 0

# Model and device setup
model_id = "sesame/csm-1b"

# generation kwargs for optimized model
gen_kwargs = {
    "do_sample": False,
    "depth_decoder_do_sample": False,
    "temperature": 1.0,
    "depth_decoder_temperature": 1.0,
}

# Timer utility for performance measurement
class TimerContext:
    def __init__(self, name="Execution"):
        self.name = name
        self.start_event = None
        self.end_event = None

    def __enter__(self):
        self.start_event = torch.cuda.Event(enable_timing=True)
        self.end_event = torch.cuda.Event(enable_timing=True)
        self.start_event.record()
        return self

    def __exit__(self, *args):
        self.end_event.record()
        torch.cuda.synchronize()
        elapsed_time = self.start_event.elapsed_time(self.end_event) / 1000.0
        logger.info(f"{self.name} time: {elapsed_time:.4f} seconds")

def load_prompt_audio(audio_path: str, target_sample_rate: int) -> torch.Tensor:
    """Load and resample audio to target sample rate."""
    audio_tensor, sample_rate = torchaudio.load(audio_path)
    audio_tensor = audio_tensor.squeeze(0)
    audio_tensor = torchaudio.functional.resample(
        audio_tensor, orig_freq=sample_rate, new_freq=target_sample_rate
    )
    return audio_tensor

async def transcribe_and_clean_audio(audio_path: str) -> str:
    """Transcribe audio file and clean the text."""
    global whisper_model
    logger.info("🎤 Transcribing audio file...")

    # 🔒 Acquire whisper model lock to prevent concurrent access
    async with whisper_model_lock:
        logger.info("🔓 Acquired Whisper model lock")
        segments, info = whisper_model.transcribe(audio_path, beam_size=5)
        logger.info(f"🗣️ Detected language: {info.language}")

        full_text = " ".join(segment.text for segment in segments)
        cleaned_text = re.sub(r'[?.!,;:\']', '', full_text.strip()).lower()
        cleaned_text = re.sub(r'\bits\b', 'its', cleaned_text)

        logger.info(f"📝 Transcribed text: {cleaned_text}")
        return cleaned_text

def prepare_prompt(text: str, speaker: int, audio_path: str, sample_rate: int) -> Segment:
    """Prepare a voice prompt segment."""
    audio_tensor = load_prompt_audio(audio_path, sample_rate)
    return Segment(text=text, speaker=speaker, audio=audio_tensor)

async def generate_speech_optimized(
    text: str,
    speaker: int,
    context: list,
    max_audio_length_ms: int
) -> torch.Tensor:
    """Optimized thread-safe wrapper for CSM model generation with static caching."""
    global model, processor

    # 🔒 Acquire CSM model lock to prevent concurrent access
    async with csm_model_lock:
        logger.info("🔓 Acquired optimized CSM model lock")

        # Prepare conversation format
        conversation = []
        for ctx in context:
            conversation.append({
                "role": str(ctx.speaker),  # Use stringified integer as required
                "content": [
                    {"type": "text", "text": ctx.text},
                    {"type": "audio", "path": ctx.audio.numpy()},
                ],
            })

        # Add the target text to generate
        conversation.append({
            "role": str(speaker),  # Use stringified integer as required
            "content": [
                {"type": "text", "text": text},
            ],
        })

        # Process inputs
        padded_inputs = processor.apply_chat_template(
            conversation,
            tokenize=True,
            return_dict=True,
        ).to(device)

        # Generate with optimized settings and timing
        with TimerContext("Optimized generation"):
            output = model.generate(**padded_inputs, **gen_kwargs)

        # Extract audio from output
        if hasattr(output, 'audio') and output.audio is not None:
            audio_tensor = output.audio.squeeze()
        else:
            # Fallback: use processor to extract audio
            audio_tensor = processor.decode_audio(output[0])

        logger.info("🔒 Released optimized CSM model lock")
        return audio_tensor

@app.on_event("startup")
async def startup_event():
    """Initialize the optimized models on startup."""
    global generator, whisper_model, device, model, processor

    device = "cuda" if torch.cuda.is_available() else "cpu"
    logger.info(f"🚀 Starting Optimized API with device: {device}")

    try:
        logger.info("Loading Whisper model...")
        whisper_load_start = time.time()
        whisper_model = WhisperModel("base", device=device, compute_type="float16")
        whisper_load_time = time.time() - whisper_load_start
        logger.info(f"✅ Whisper model loaded in {whisper_load_time:.2f} seconds")
    except Exception:
        logger.exception("❌ Failed to load Whisper model")

    try:
        logger.info("Loading optimized CSM model...")
        model_load_start = time.time()

        # Load the optimized model with static caching
        torch._logging.set_logs(graph_breaks=True, recompiles=True, cudagraphs=True)

        processor = AutoProcessor.from_pretrained(model_id)
        model = CsmForConditionalGeneration.from_pretrained(model_id, device_map=device)

        # Enable static CUDA caching for performance
        model.generation_config.max_length = 250  # big enough to avoid recompilation
        model.generation_config.max_new_tokens = None  # would take precedence over max_length
        model.generation_config.cache_implementation = "static"
        model.depth_decoder.generation_config.cache_implementation = "static"

        # Also load the generator for compatibility
        generator = load_csm_1b(device)

        model_load_time = time.time() - model_load_start
        logger.info(f"✅ Optimized CSM model loaded in {model_load_time:.2f} seconds")
        logger.info("🚀 Static caching enabled for maximum performance!")
    except Exception:
        logger.exception("❌ Failed to load optimized CSM model")

@app.get("/")
async def root():
    """Health check endpoint."""
    return {
        "message": "CSM Optimized Voice Generation API is running",
        "device": device,
        "csm_model_loaded": model is not None,
        "whisper_model_loaded": whisper_model is not None,
        "optimizations": "Static caching + CUDA graphs enabled"
    }

@app.post("/generate-voice")
async def generate_voice(
    text: str = Form(..., description="Text to generate speech for"),
    voice_file: UploadFile = File(..., description="Voice sample file (WAV format)"),
    max_audio_length_ms: Optional[int] = Form(30000, description="Maximum audio length in milliseconds")
):
    """Generate speech using the optimized CSM model with static caching."""
    global active_requests, total_requests

    # 📊 Track request metrics
    total_requests += 1
    request_id = f"opt_req_{total_requests:04d}"
    logger.info(f"🚀 [{request_id}] Starting optimized voice generation request")

    if model is None or whisper_model is None:
        raise HTTPException(status_code=503, detail="Optimized models not loaded yet")

    if not voice_file.filename.lower().endswith(('.wav', '.mp3', '.flac', '.m4a')):
        raise HTTPException(status_code=400, detail="Voice file must be an audio file (WAV, MP3, FLAC, M4A)")

    # 📊 Update active request count
    active_requests += 1
    logger.info(f"📈 [{request_id}] Active requests: {active_requests}")

    try:
        logger.info(f"📥 [{request_id}] Received voice sample: {voice_file.filename}")
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as temp_voice_file:
            content = await voice_file.read()
            temp_voice_file.write(content)
            temp_voice_path = temp_voice_file.name

        # 🎤 Transcribe audio using thread-safe function
        voice_text = await transcribe_and_clean_audio(temp_voice_path)

        logger.info(f"🎵 [{request_id}] Preparing voice prompt from uploaded file...")
        prompt_segment = prepare_prompt(
            text=voice_text,
            speaker=0,
            audio_path=temp_voice_path,
            sample_rate=generator.sample_rate if generator else 24000
        )

        logger.info(f"🎵 [{request_id}] Generating speech with optimized model for: {text[:50]}...")
        generation_start = time.time()

        # 🎵 Generate audio using optimized function
        audio_tensor = await generate_speech_optimized(
            text=text,
            speaker=0,
            context=[prompt_segment],
            max_audio_length_ms=max_audio_length_ms,
        )

        generation_time = time.time() - generation_start
        logger.info(f"✅ [{request_id}] Optimized speech generated in {generation_time:.2f} seconds")

        output_filename = f"optimized_speech_{uuid.uuid4().hex[:8]}.wav"
        output_path = f"/tmp/{output_filename}"

        torchaudio.save(
            output_path,
            audio_tensor.unsqueeze(0).cpu(),
            generator.sample_rate if generator else 24000
        )

        logger.info(f"💾 [{request_id}] Saved optimized file to {output_path}")
        os.unlink(temp_voice_path)

        # 📊 Update request metrics before returning
        active_requests -= 1
        logger.info(f"📉 [{request_id}] Request completed. Active requests: {active_requests}")

        return FileResponse(
            output_path,
            media_type="audio/wav",
            filename=output_filename,
            headers={
                "X-Generation-Time": str(generation_time),
                "X-Request-ID": request_id,
                "X-Optimization": "static-caching-enabled"
            }
        )

    except Exception as e:
        # 📊 Update request metrics on error
        active_requests -= 1
        logger.exception(f"❌ [{request_id}] Error during optimized voice generation")
        if 'temp_voice_path' in locals() and os.path.exists(temp_voice_path):
            os.unlink(temp_voice_path)
        raise HTTPException(status_code=500, detail=f"Error generating speech: {str(e)}")

@app.get("/health")
async def health_check():
    """Detailed health check for optimized API."""
    global active_requests, total_requests
    logger.info("🔍 Optimized health check requested")

    # Check if models are currently locked
    csm_locked = csm_model_lock.locked()
    whisper_locked = whisper_model_lock.locked()

    return {
        "status": "healthy",
        "device": device,
        "csm_model_loaded": model is not None,
        "whisper_model_loaded": whisper_model is not None,
        "cuda_available": torch.cuda.is_available(),
        "gpu_memory": torch.cuda.get_device_properties(0).total_memory if torch.cuda.is_available() else None,
        "optimizations": {
            "static_caching": True,
            "cuda_graphs": True,
            "torch_compile": True
        },
        "concurrency_info": {
            "active_requests": active_requests,
            "total_requests": total_requests,
            "csm_model_locked": csm_locked,
            "whisper_model_locked": whisper_locked
        }
    }

@app.get("/queue-status")
async def queue_status():
    """Get current queue and concurrency status for optimized API."""
    global active_requests, total_requests

    return {
        "active_requests": active_requests,
        "total_requests": total_requests,
        "csm_model_locked": csm_model_lock.locked(),
        "whisper_model_locked": whisper_model_lock.locked(),
        "optimization_status": "static-caching-enabled",
        "queue_info": {
            "csm_waiters": len(csm_model_lock._waiters) if hasattr(csm_model_lock, '_waiters') else 0,
            "whisper_waiters": len(whisper_model_lock._waiters) if hasattr(whisper_model_lock, '_waiters') else 0
        }
    }

@app.post("/benchmark")
async def benchmark_performance():
    """Benchmark the optimized model performance."""
    if model is None or processor is None:
        raise HTTPException(status_code=503, detail="Optimized models not loaded yet")

    logger.info("🏁 Starting performance benchmark...")

    # Create dummy conversation for benchmarking
    dummy_conversation = [
        {
            "role": "0",  # Use stringified integer as required
            "content": [
                {"type": "text", "text": "Hello, how are you today?"},
                {"type": "audio", "path": torch.randn(24000).numpy()},  # 1 second of dummy audio
            ],
        },
        {
            "role": "1",  # Use stringified integer as required
            "content": [
                {"type": "text", "text": "I'm doing great, thanks for asking!"},
            ],
        },
    ]

    async with csm_model_lock:
        # First generation (compilation + CUDA graph recording)
        logger.info("🔥 First generation - compiling and recording CUDA graphs...")
        padded_inputs = processor.apply_chat_template(
            dummy_conversation,
            tokenize=True,
            return_dict=True,
        ).to(device)

        with TimerContext("First generation (compilation)"):
            _ = model.generate(**padded_inputs, **gen_kwargs)

        # Second generation (fast with CUDA graphs)
        logger.info("⚡ Second generation - using CUDA graphs...")
        with TimerContext("Second generation (optimized)"):
            _ = model.generate(**padded_inputs, **gen_kwargs)

        # Third generation (same input, maximum speed)
        logger.info("🚀 Third generation - maximum speed...")
        with TimerContext("Third generation (maximum speed)"):
            _ = model.generate(**padded_inputs, **gen_kwargs)

    return {
        "message": "Benchmark completed - check logs for timing results",
        "optimization_status": "static-caching-enabled",
        "note": "First generation includes compilation overhead, subsequent generations are optimized"
    }

if __name__ == "__main__":
    uvicorn.run(
        "test_concurrent:app",
        host="0.0.0.0",
        port=8001,  # Different port from voice_api.py
        reload=False
    )
