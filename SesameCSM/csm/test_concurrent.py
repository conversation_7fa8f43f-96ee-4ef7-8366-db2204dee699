import torch
from transformers import CsmForConditionalGeneration, AutoProcessor
from datasets import load_dataset
import os

# Model and device setup
model_id = "sesame/csm-1b"
device = "cuda"

# Enable Torch compile logs for graph tracing
torch._logging.set_logs(graph_breaks=True, recompiles=True, cudagraphs=True)

# Load processor and model
processor = AutoProcessor.from_pretrained(model_id)
model = CsmForConditionalGeneration.from_pretrained(model_id, device_map=device)

# Enable static CUDA caching
model.generation_config.max_length = 250
model.generation_config.max_new_tokens = None
model.generation_config.cache_implementation = "static"
model.depth_decoder.generation_config.cache_implementation = "static"

# Generation arguments
gen_kwargs = {
    "do_sample": False,
    "depth_decoder_do_sample": False,
    "temperature": 1.0,
    "depth_decoder_temperature": 1.0,
    "output_audio": True  # 👈 IMPORTANT: tells the model to generate audio
}

# Timer utility
class TimerContext:
    def __init__(self, name="Execution"):
        self.name = name
        self.start_event = None
        self.end_event = None
        
    def __enter__(self):
        self.start_event = torch.cuda.Event(enable_timing=True)
        self.end_event = torch.cuda.Event(enable_timing=True)
        self.start_event.record()
        return self

    def __exit__(self, *args):
        self.end_event.record()
        torch.cuda.synchronize()
        elapsed_time = self.start_event.elapsed_time(self.end_event) / 1000.0
        print(f"{self.name} time: {elapsed_time:.4f} seconds")

# Save audio helper
def generate_and_save(model, processor, inputs, filename):
    with TimerContext(f"Generating {filename}"):
        output = model.generate(**inputs, **gen_kwargs)
    processor.save_audio(output[0], filename)
    print(f"✅ Saved: {filename}")

# Load dataset
ds = load_dataset("hf-internal-testing/dailytalk-dummy", split="train")

# Step 1: First conversation
conversation_1 = [
    {"role": f"{ds[0]['speaker_id']}", "content": [
        {"type": "text", "text": ds[0]["text"]},
        {"type": "audio", "path": ds[0]["audio"]["array"]},
    ]},
    {"role": f"{ds[1]['speaker_id']}", "content": [
        {"type": "text", "text": ds[1]["text"]},
        {"type": "audio", "path": ds[1]["audio"]["array"]},
    ]},
    {"role": f"{ds[2]['speaker_id']}", "content": [
        {"type": "text", "text": ds[2]["text"]},
    ]},
]

padded_inputs_1 = processor.apply_chat_template(
    conversation_1, tokenize=True, return_dict=True
).to(device)

# Step 2: Second conversation (same input, different cache reuse)
padded_inputs_2 = padded_inputs_1  # same as step 1

# Step 3: Different conversation
conversation_3 = [
    {"role": f"{ds[0]['speaker_id']}", "content": [
        {"type": "text", "text": ds[2]["text"]},
        {"type": "audio", "path": ds[2]["audio"]["array"]},
    ]},
    {"role": f"{ds[1]['speaker_id']}", "content": [
        {"type": "text", "text": ds[3]["text"]},
        {"type": "audio", "path": ds[3]["audio"]["array"]},
    ]},
    {"role": f"{ds[2]['speaker_id']}", "content": [
        {"type": "text", "text": ds[4]["text"]},
    ]},
]

padded_inputs_3 = processor.apply_chat_template(
    conversation_3, tokenize=True, return_dict=True
).to(device)

# Generate and save audio for each step
generate_and_save(model, processor, padded_inputs_1, "output_step1.wav")
generate_and_save(model, processor, padded_inputs_2, "output_step2.wav")
generate_and_save(model, processor, padded_inputs_3, "output_step3.wav")
